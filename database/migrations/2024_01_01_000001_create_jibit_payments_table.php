<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jibit_payments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('payment_identifier')->unique(); // شناسه پرداخت جیبیت
            $table->string('psp_switching_url')->nullable(); // URL انتقال به درگاه
            $table->decimal('amount', 15, 2); // مبلغ به تومان
            $table->string('currency', 3)->default('T'); // T for Toman, R for Rial
            $table->string('description')->nullable();
            $table->string('status')->default('pending'); // pending, paid, failed, expired
            $table->string('reference_number')->nullable(); // شماره مرجع بانک
            $table->string('trace_number')->nullable(); // شماره پیگیری
            $table->timestamp('paid_at')->nullable();
            $table->json('callback_data')->nullable(); // داده‌های دریافتی از callback
            $table->json('request_data')->nullable(); // داده‌های ارسالی به جیبیت
            $table->json('response_data')->nullable(); // پاسخ دریافتی از جیبیت
            $table->string('ip_address')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['user_id', 'status']);
            $table->index('payment_identifier');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jibit_payments');
    }
};
