<?php

use App\Http\Controllers\AlertController;
use App\Http\Controllers\DepositWalletController;
use App\Http\Controllers\FiatWalletWithdrawalController;
use App\Http\Controllers\User\CardController;
use App\Http\Controllers\User\CurrencyController;
use App\Http\Controllers\User\DepositController;
use App\Http\Controllers\User\DocumentController;
use App\Http\Controllers\User\GiftCardController;
use App\Http\Controllers\User\NationalCardController;
use App\Http\Controllers\User\SupportController;
use App\Http\Controllers\User\TradeController;
use App\Http\Controllers\User\TransactionController;
use App\Http\Controllers\User\UserMainController;
use App\Http\Controllers\User\WalletController;
use App\Http\Controllers\User\WithdrawController;
use App\Http\Controllers\User\JibitPaymentController;
use App\Http\Controllers\User\TomanWithdrawalController;
use App\Http\Controllers\User\LoginController;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\DecryptRSAInput;

Route::prefix('user')->middleware(['auth:sanctum'])->name('user.')->group(function () {
    // User Profile routes with selective middleware
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [UserMainController::class, 'index'])->name('index');
        Route::post('/', [UserMainController::class, 'store'])->name('store')->middleware('decrypt.rsa');
        Route::get('/{user}', [UserMainController::class, 'show'])->name('show');
        Route::put('/{user}', [UserMainController::class, 'update'])->name('update')->middleware('decrypt.rsa');
        Route::delete('/{user}', [UserMainController::class, 'destroy'])->name('destroy');
    });

    Route::prefix('/document')->group(function () {
        Route::controller(DocumentController::class)->group(function () {
            Route::get('', 'index')->name('index');
            Route::post('', 'store')->name('store');
            Route::get('verification-status', 'verificationStatus')->name('verification-status');
            Route::get('{id}', 'show')->name('show');
        });
    });
    // Card routes with selective middleware
    Route::get('card', [CardController::class, 'index'])->name('card.index');
    Route::post('card', [CardController::class, 'store'])->name('card.store')->middleware('decrypt.rsa');
    Route::get('card/{card}', [CardController::class, 'show'])->name('card.show');
    Route::put('card/{card}', [CardController::class, 'update'])->name('card.update')->middleware('decrypt.rsa');
    Route::delete('card/{card}', [CardController::class, 'destroy'])->name('card.destroy');

    // National Card routes with selective middleware
    Route::get('national-card', [NationalCardController::class, 'index'])->name('national-card.index');
    Route::post('national-card', [NationalCardController::class, 'store'])->name('national-card.store')->middleware('decrypt.rsa');
    Route::get('national-card/{national_card}', [NationalCardController::class, 'show'])->name('national-card.show');
    Route::put('national-card/{national_card}', [NationalCardController::class, 'update'])->name('national-card.update')->middleware('decrypt.rsa');
    Route::delete('national-card/{national_card}', [NationalCardController::class, 'destroy'])->name('national-card.destroy');
    Route::prefix('support')->name('support.')->group(function () {
        Route::controller(SupportController::class)->group(function () {
            Route::get('', 'index')->name('index');
            Route::post('', 'store')->name('store')->middleware('decrypt.rsa');
            Route::get('{id}', 'show')->name('show');
            Route::post('{id}', 'update')->name('update')->middleware('decrypt.rsa');
            Route::delete('{id}', 'destroy')->name('destroy');
            Route::post('{id}/reply',  'reply')->middleware('decrypt.rsa');
        });
    });
    // Route::post('support/{id}/reply', [SupportController::class, 'reply']);

    Route::prefix('trade')->name('trade.')->group(function () {
        Route::controller(TradeController::class)->group(function () {
            Route::get('', 'index')->name('index');
            Route::post('', 'store')->name('store')->middleware('decrypt.rsa');
            Route::get('{id}', 'show')->name('show');
            Route::post('{id}', 'update')->name('update')->middleware('decrypt.rsa');
            Route::delete('{id}', 'destroy')->name('destroy');
        });
    });

    // Buy, Sell, and Swap API endpoints
    Route::prefix('trading')->name('trading.')->group(function () {
        Route::controller(\App\Http\Controllers\User\TradingController::class)->group(function () {
            Route::post('buy', 'buy')->name('buy')->middleware('decrypt.rsa');
            Route::post('sell', 'sell')->name('sell')->middleware('decrypt.rsa');
            Route::post('swap', 'swap')->name('swap')->middleware('decrypt.rsa');
            Route::get('swap-history', 'swapHistory')->name('swap-history');
            Route::get('transactions', 'transactionHistory')->name('transactions');
            Route::get('transaction/{id}', 'getTransaction')->name('transaction');
            Route::get('daily-limits', 'dailyLimits')->name('daily-limits');
        });
    });

    // Toman Withdrawal routes with selective middleware
    Route::get('toman-withdrawal', [App\Http\Controllers\User\TomanWithdrawalController::class, 'index'])->name('toman-withdrawal.index');
    Route::post('toman-withdrawal', [App\Http\Controllers\User\TomanWithdrawalController::class, 'store'])->name('toman-withdrawal.store')->middleware('decrypt.rsa');
    Route::get('toman-withdrawal/{toman_withdrawal}', [App\Http\Controllers\User\TomanWithdrawalController::class, 'show'])->name('toman-withdrawal.show');
    Route::get('toman-withdrawal/check-2fa', [App\Http\Controllers\User\TomanWithdrawalController::class, 'check2fa']);

    // Toman Deposit routes with selective middleware
    Route::get('toman-deposit', [App\Http\Controllers\User\TomanDepositController::class, 'index'])->name('toman-deposit.index');
    Route::post('toman-deposit', [App\Http\Controllers\User\TomanDepositController::class, 'store'])->name('toman-deposit.store')->middleware('decrypt.rsa');
    Route::get('toman-deposit/{toman_deposit}', [App\Http\Controllers\User\TomanDepositController::class, 'show'])->name('toman-deposit.show');


    // Route::apiResource('wallet', WalletController::class);
    Route::prefix('wallet')->name('wallet.')->group(function () {
        Route::controller(WalletController::class)->group(function () {
            Route::get('wallet-list', 'walletList')->name('walletList');
            Route::get('wallet-total-value', 'walletTotalValue')->name('walletTotalValue');
            Route::get('key-statistics', 'keyStatistics')->name('keyStatistics');
            Route::get('wallet-deposit-{id}', 'walletDeposit')->name('walletDeposit');
            Route::get('wallet-withdrawal-{id}', 'walletWithdrawal')->name('walletWithdrawal');
            Route::post('wallet-withdrawal-process', 'walletWithdrawalProcess')->name('walletWithdrawalProcess')->middleware('decrypt.rsa');
            Route::post('pre-withdrawal-process', 'preWithdrawalProcess')->name('preWithdrawalProcess')->middleware('decrypt.rsa');
            Route::post('get-wallet-network-address', 'getWalletNetworkAddress')->name('getWalletNetworkAddress')->middleware('decrypt.rsa');
            // Route::get('', 'index')->name('index');
            // Route::post('', 'store')->name('store');
            // Route::get('{id}', 'show')->name('show');
            // Route::post('{id}', 'update')->name('update');
            // Route::delete('{id}', 'destroy')->name('destroy');
        });
    });
    Route::post('deposit/wallet-address', [DepositWalletController::class, 'getWalletAddress'])->middleware('decrypt.rsa');

    // Deposit routes with selective middleware
    Route::get('deposit', [DepositController::class, 'index'])->name('deposit.index');
    Route::post('deposit', [DepositController::class, 'store'])->name('deposit.store')->middleware('decrypt.rsa');
    Route::get('deposit/{deposit}', [DepositController::class, 'show'])->name('deposit.show');

    // Currency routes (read-only)
    Route::get('currency', [CurrencyController::class, 'index'])->name('currency.index');
    Route::get('currency/{currency}', [CurrencyController::class, 'show'])->name('currency.show');

    //Documents

    //Cards
    // Gift Card routes with selective middleware
    Route::get('gift-card', [GiftCardController::class, 'index'])->name('gift-card.index');
    Route::post('gift-card', [GiftCardController::class, 'store'])->name('gift-card.store')->middleware('decrypt.rsa');
    Route::get('gift-card/{gift_card}', [GiftCardController::class, 'show'])->name('gift-card.show');

    // Transaction routes (read-only)
    Route::get('transaction', [TransactionController::class, 'index'])->name('transaction.index');
    Route::get('transaction/{transaction}', [TransactionController::class, 'show'])->name('transaction.show');
    Route::get('transaction-totals', [TransactionController::class, 'totals']);

    // Wallets routes with selective middleware
    Route::get('wallets', [WalletController::class, 'index'])->name('wallets.index');
    Route::post('wallets', [WalletController::class, 'store'])->name('wallets.store')->middleware('decrypt.rsa');

    // Wallet transactions (read-only)
    Route::get('wallets/{wallet}/transactions', [TransactionController::class, 'index'])->name('wallets.transactions.index');
    Route::get('wallets/{wallet}/transactions/{transaction}', [TransactionController::class, 'show'])->name('wallets.transactions.show');
    Route::post('alert/{id}', AlertController::class)->middleware('decrypt.rsa');

    // Login history
    Route::get('login-history', [LoginController::class, 'getLoginHistory']);

    // Deposit Transactions API
    Route::prefix('deposit-transactions')->name('deposit-transactions.')->group(function () {
        Route::controller(App\Http\Controllers\Api\DepositTransactionController::class)->group(function () {
            Route::get('/', 'index')->name('index');
            Route::get('summary', 'summary')->name('summary');
            Route::get('available-coins', 'availableCoins')->name('available-coins');
            Route::get('available-networks', 'availableNetworks')->name('available-networks');
            Route::get('{id}', 'show')->name('show');
        });
    });

    // 2FA Management
    Route::prefix('2fa')->name('2fa.')->group(function () {
        Route::controller(App\Http\Controllers\User\TwoFactorAuthController::class)->group(function () {
            Route::get('status', 'status')->name('status');
            Route::get('generate', 'generate')->name('generate');
            Route::post('enable', 'enable')->name('enable')->middleware('decrypt.rsa');
            Route::post('disable', 'disable')->name('disable')->middleware('decrypt.rsa');
            Route::post('reset', 'reset')->name('reset')->middleware('decrypt.rsa');
        });
    });

    // Jibit Payment Routes
    Route::prefix('jibit-payment')->name('jibit-payment.')->group(function () {
        Route::controller(JibitPaymentController::class)->group(function () {
            Route::post('create', 'createPayment')->name('create');
            Route::get('{paymentIdentifier}/status', 'getPaymentStatus')->name('status');
            Route::post('{paymentIdentifier}/verify', 'verifyPayment')->name('verify');
            Route::post('{paymentIdentifier}/cancel', 'cancelPayment')->name('cancel');
            Route::get('history', 'getPaymentHistory')->name('history');
            Route::get('stats', 'getPaymentStats')->name('stats');
        });
    });
});
