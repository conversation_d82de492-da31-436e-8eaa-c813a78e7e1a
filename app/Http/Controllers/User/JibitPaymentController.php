<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Services\JibitPaymentService;
use App\Models\JibitPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class JibitPaymentController extends Controller
{
    protected $jibitService;

    public function __construct(JibitPaymentService $jibitService)
    {
        $this->jibitService = $jibitService;
    }

    /**
     * Create a new payment identifier
     */
    public function createPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:10000|max:50000000', // حداقل 10,000 تومان، حداکثر 50,000,000 تومان
            'description' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'داده‌های ورودی نامعتبر است',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $amount = $request->input('amount');
        $description = $request->input('description', 'شارژ کیف پول تومانی');

        $result = $this->jibitService->createPaymentIdentifier($user, $amount, $description);

        if ($result['success']) {
            return response()->json($result, 201);
        }

        return response()->json($result, 400);
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus(Request $request, $paymentIdentifier)
    {
        $user = Auth::user();
        
        $payment = JibitPayment::where('payment_identifier', $paymentIdentifier)
            ->where('user_id', $user->id)
            ->first();

        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => 'شناسه پرداخت یافت نشد'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'payment_id' => $payment->id,
                'payment_identifier' => $payment->payment_identifier,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'status' => $payment->status,
                'description' => $payment->description,
                'created_at' => $payment->created_at,
                'paid_at' => $payment->paid_at,
                'reference_number' => $payment->reference_number,
                'trace_number' => $payment->trace_number,
            ]
        ]);
    }

    /**
     * Get payment ID status
     */
    public function getPaymentIdStatus(Request $request, $merchantReferenceNumber)
    {
        $user = Auth::user();

        $payment = JibitPayment::where('payment_identifier', $merchantReferenceNumber)
            ->where('user_id', $user->id)
            ->first();

        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => 'شناسه پرداخت یافت نشد'
            ], 404);
        }

        $result = $this->jibitService->getPaymentIdStatus($merchantReferenceNumber);

        return response()->json($result);
    }

    /**
     * Check for payments and process them
     */
    public function checkPayments(Request $request, $merchantReferenceNumber)
    {
        $user = Auth::user();

        $payment = JibitPayment::where('payment_identifier', $merchantReferenceNumber)
            ->where('user_id', $user->id)
            ->first();

        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => 'شناسه پرداخت یافت نشد'
            ], 404);
        }

        $result = $this->jibitService->getPayments($merchantReferenceNumber);

        return response()->json($result);
    }

    /**
     * Get user's payment history
     */
    public function getPaymentHistory(Request $request)
    {
        $user = Auth::user();
        $perPage = $request->input('per_page', 15);

        $payments = JibitPayment::forUser($user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $payments->items(),
            'pagination' => [
                'current_page' => $payments->currentPage(),
                'last_page' => $payments->lastPage(),
                'per_page' => $payments->perPage(),
                'total' => $payments->total(),
            ]
        ]);
    }

    /**
     * Get payment statistics
     */
    public function getPaymentStats(Request $request)
    {
        $user = Auth::user();

        $stats = [
            'total_payments' => JibitPayment::forUser($user->id)->count(),
            'successful_payments' => JibitPayment::forUser($user->id)->paid()->count(),
            'pending_payments' => JibitPayment::forUser($user->id)->pending()->count(),
            'failed_payments' => JibitPayment::forUser($user->id)->failed()->count(),
            'total_amount_paid' => JibitPayment::forUser($user->id)->paid()->sum('amount'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Cancel pending payment
     */
    public function cancelPayment(Request $request, $paymentIdentifier)
    {
        $user = Auth::user();
        
        $payment = JibitPayment::where('payment_identifier', $paymentIdentifier)
            ->where('user_id', $user->id)
            ->where('status', 'pending')
            ->first();

        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => 'پرداخت قابل لغو یافت نشد'
            ], 404);
        }

        $payment->update(['status' => 'cancelled']);

        return response()->json([
            'success' => true,
            'message' => 'پرداخت با موفقیت لغو شد'
        ]);
    }
}
