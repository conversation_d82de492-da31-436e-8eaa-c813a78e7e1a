<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class JibitPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => [
                'required',
                'numeric',
                'min:10000', // حداقل 10,000 تومان
                'max:50000000', // حداکثر 50,000,000 تومان
            ],
            'description' => [
                'nullable',
                'string',
                'max:255'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'amount.required' => 'مبلغ الزامی است',
            'amount.numeric' => 'مبلغ باید عددی باشد',
            'amount.min' => 'حداقل مبلغ قابل پرداخت 10,000 تومان است',
            'amount.max' => 'حداکثر مبلغ قابل پرداخت 50,000,000 تومان است',
            'description.string' => 'توضیحات باید متن باشد',
            'description.max' => 'توضیحات نباید بیشتر از 255 کاراکتر باشد',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'amount' => 'مبلغ',
            'description' => 'توضیحات',
        ];
    }
}
