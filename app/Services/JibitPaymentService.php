<?php

namespace App\Services;

use App\Models\JibitPayment;
use App\Models\User;
use App\Models\Transaction;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class JibitPaymentService
{
    protected $baseUrl;
    protected $apiKey;
    protected $secretKey;
    protected $callbackUrl;

    public function __construct()
    {
        $this->baseUrl = config('payment.drivers.jibit_pip.base_url');
        $this->apiKey = config('payment.drivers.jibit_pip.api_key');
        $this->secretKey = config('payment.drivers.jibit_pip.secret_key');
        $this->callbackUrl = config('payment.drivers.jibit_pip.callback_url');
    }

    /**
     * Create a new payment identifier
     */
    public function createPaymentIdentifier(User $user, float $amount, string $description = null): array
    {
        try {
            DB::beginTransaction();

            // Generate unique payment identifier
            $paymentIdentifier = $this->generatePaymentIdentifier();

            // Prepare request data
            $requestData = [
                'paymentIdentifier' => $paymentIdentifier,
                'amount' => $amount,
                'currency' => config('payment.drivers.jibit_pip.currency', 'T'),
                'description' => $description ?? config('payment.drivers.jibit_pip.description'),
                'callbackUrl' => $this->callbackUrl,
                'additionalData' => [
                    'userId' => $user->id,
                    'userPhone' => $user->phone ?? '',
                ]
            ];

            // Send request to Jibit API
            $response = $this->sendApiRequest('/payment-identifiers', $requestData);

            if (!$response['success']) {
                throw new \Exception($response['message'] ?? 'خطا در ایجاد شناسه پرداخت');
            }

            // Create payment record
            $payment = JibitPayment::create([
                'user_id' => $user->id,
                'payment_identifier' => $paymentIdentifier,
                'psp_switching_url' => $response['data']['pspSwitchingUrl'] ?? null,
                'amount' => $amount,
                'currency' => config('payment.drivers.jibit_pip.currency', 'T'),
                'description' => $description ?? config('payment.drivers.jibit_pip.description'),
                'status' => 'pending',
                'request_data' => $requestData,
                'response_data' => $response['data'] ?? [],
                'ip_address' => request()->ip(),
            ]);

            DB::commit();

            return [
                'success' => true,
                'data' => [
                    'payment_id' => $payment->id,
                    'payment_identifier' => $paymentIdentifier,
                    'psp_switching_url' => $response['data']['pspSwitchingUrl'] ?? null,
                    'amount' => $amount,
                    'currency' => config('payment.drivers.jibit_pip.currency', 'T'),
                ],
                'message' => 'شناسه پرداخت با موفقیت ایجاد شد'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Jibit Payment Creation Error', [
                'user_id' => $user->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'خطا در ایجاد شناسه پرداخت: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Verify payment status
     */
    public function verifyPayment(string $paymentIdentifier): array
    {
        try {
            $payment = JibitPayment::where('payment_identifier', $paymentIdentifier)->first();

            if (!$payment) {
                return [
                    'success' => false,
                    'message' => 'شناسه پرداخت یافت نشد'
                ];
            }

            // Send verification request to Jibit API
            $response = $this->sendApiRequest("/payment-identifiers/{$paymentIdentifier}/verify", [], 'GET');

            if (!$response['success']) {
                return [
                    'success' => false,
                    'message' => $response['message'] ?? 'خطا در تایید پرداخت'
                ];
            }

            $paymentData = $response['data'];
            $isSuccessful = $paymentData['status'] === 'SUCCESSFUL';

            if ($isSuccessful && $payment->isPending()) {
                // Process successful payment
                $this->processSuccessfulPayment($payment, $paymentData);
            }

            return [
                'success' => true,
                'data' => [
                    'payment_id' => $payment->id,
                    'status' => $paymentData['status'],
                    'amount' => $payment->amount,
                    'is_successful' => $isSuccessful,
                ],
                'message' => $isSuccessful ? 'پرداخت با موفقیت انجام شد' : 'پرداخت ناموفق بود'
            ];

        } catch (\Exception $e) {
            Log::error('Jibit Payment Verification Error', [
                'payment_identifier' => $paymentIdentifier,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'خطا در تایید پرداخت: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process successful payment and charge user balance
     */
    protected function processSuccessfulPayment(JibitPayment $payment, array $paymentData): void
    {
        DB::beginTransaction();

        try {
            // Mark payment as paid
            $payment->markAsPaid($paymentData);

            // Get user
            $user = $payment->user;

            // Store balance before transaction
            $balanceBefore = $user->toman_balance ?? 0;

            // Add amount to user's toman balance
            $user->toman_balance = $balanceBefore + $payment->amount;
            $user->save();

            // Create transaction record
            $user->transactions()->create([
                'type' => 'deposit',
                'amount' => $payment->amount,
                'currency_id' => 3, // Toman/IRR currency ID
                'status' => 'done',
                'description' => 'شارژ کیف پول تومانی از طریق جیبیت - ' . $payment->description,
                'transaction_id' => $payment->payment_identifier,
                'balance_before' => $balanceBefore,
                'balance_after' => $user->toman_balance,
                'ip' => $payment->ip_address,
                'details' => [
                    'jibit_payment_id' => $payment->id,
                    'reference_number' => $payment->reference_number,
                    'trace_number' => $payment->trace_number,
                ]
            ]);

            DB::commit();

            Log::info('Jibit Payment Processed Successfully', [
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'amount' => $payment->amount,
                'new_balance' => $user->toman_balance
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error Processing Jibit Payment', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Send API request to Jibit
     */
    protected function sendApiRequest(string $endpoint, array $data = [], string $method = 'POST'): array
    {
        try {
            $url = $this->baseUrl . $endpoint;
            
            $headers = [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ];

            $response = Http::withHeaders($headers)
                ->timeout(30);

            if ($method === 'POST') {
                $response = $response->post($url, $data);
            } else {
                $response = $response->get($url, $data);
            }

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            return [
                'success' => false,
                'message' => 'خطا در ارتباط با سرویس پرداخت',
                'error_code' => $response->status(),
                'error_data' => $response->json()
            ];

        } catch (\Exception $e) {
            Log::error('Jibit API Request Error', [
                'endpoint' => $endpoint,
                'method' => $method,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'خطا در ارتباط با سرویس پرداخت: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate unique payment identifier
     */
    protected function generatePaymentIdentifier(): string
    {
        do {
            $identifier = 'PIP_' . time() . '_' . Str::random(8);
        } while (JibitPayment::where('payment_identifier', $identifier)->exists());

        return $identifier;
    }
}
